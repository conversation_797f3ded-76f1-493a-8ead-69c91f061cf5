# 特斯拉维修工单邮件爬虫配置

# 邮箱配置
email:
  # 邮箱服务器配置
  imap_server: "imap.163.com"
  imap_port: 143
  imap_ssl: true

  # 邮箱账号配置
  username: "<EMAIL>"
  password: "QWwLfrAH2Ni6uhbw"

  # 邮件扫描配置
  scan_folder: "INBOX"  # 需要扫描的文件夹
  sender_filter: "@tesla.com"     # 发件人过滤，使用@开头表示过滤指定域名后缀，留空表示不过滤
  subject_filter: "订单转入"  # 邮件主题过滤，留空表示不过滤
  days_back: 1          # 扫描多少天前的邮件

# 特斯拉合作伙伴门户系统配置
# 登录配置
login:
  url: "https://partners.tesla.cn/home/<USER>"  # 登录URL
  username: "<EMAIL>"  # 用户名
  password: "TSL2008#"  # 密码
  remember_me: true  # 是否记住登录状态
  storage_state_path: "storage/auth/tesla_repair_auth.json"  # 存储状态路径
  max_login_retries: 3  # 最大登录重试次数

# 工单系统配置
tesla_pp:
  base_url: "https://akamai-apigateway-partnerportalcn.tesla.cn"  # 工单系统基础URL
  order_list_url: "https://akamai-apigateway-partnerportalcn.tesla.cn/installerapi/installation/search"
  order_detail_url: "https://akamai-apigateway-partnerportalcn.tesla.cn/installerapi/installation/detail"
  order_status_url: "https://akamai-apigateway-partnerportalcn.tesla.cn/installerapi/installation/status/accept"  # 工单状态更新URL

  # 获取工单的参数配置
  fetch:
    timeout: 60  # 请求超时时间(秒)
    page_size: 25  # 每页数量