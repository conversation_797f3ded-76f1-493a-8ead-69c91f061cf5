#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
爬虫主入口模块
负责启动和管理各种爬虫
"""
import argparse
import logging
import os
import sys

# 添加当前目录到PATH
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from spiders.base.config import ConfigManager
from spiders.base.utils.logger import configure_logging
from spiders.registry import get_all_spiders, create_spider


def parse_args():
    """解析命令行参数"""
    # 获取所有爬虫名称
    spider_names = list(get_all_spiders().keys())
    
    parser = argparse.ArgumentParser(description='News Spider 爬虫系统')
    parser.add_argument('--spider', type=str, default='tesla_transfer_order' if 'tesla_transfer_order' in spider_names else spider_names[0],
                        choices=spider_names,
                        help='要运行的爬虫名称')
    parser.add_argument('--config', type=str, default='conf/config.yaml', help='配置文件路径')
    parser.add_argument('--env', type=str, default='dev',
                        choices=['dev', 'test', 'prod'], 
                        help='运行环境，默认从配置文件读取，可选: dev(开发环境), test(测试环境), prod(生产环境)')
    parser.add_argument('--log-level', type=str, default=None,
                        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                        help='日志级别')

    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    # 创建ConfigManager并加载配置
    config_manager = ConfigManager(args.config, args.env)
    # 如果命令行指定了日志级别，则覆盖配置中的设置
    if args.log_level:
        # 修改配置对象中的日志级别
        config_manager.set('logging.level', args.log_level)
    # 配置日志系统
    configure_logging(config_manager)
    # 获取主程序日志器
    logger = logging.getLogger("main")

    # 记录配置文件和环境信息
    logger.info(f"成功加载配置文件: {args.config}")
    logger.info(f"当前运行环境: {config_manager.get_env()}")
    logger.info(f"当前日志级别: {config_manager.get('logging.level')}")

    # 创建爬虫实例
    try:
        if not args.spider:
            logger.error("未指定爬虫名称")
            sys.exit(1)
        logger.info(f"正在创建爬虫: {args.spider}")
        spider = create_spider(args.spider, config_manager)
    except Exception as e:
        logger.error(f"创建爬虫失败: {str(e)}", exc_info=True)
        sys.exit(1)

    # 执行爬虫
    try:
        # 运行爬虫
        logger.info(f"开始运行爬虫: {args.spider}")
        success = spider.run()
        if not success:
            logger.error("爬虫运行失败")
            sys.exit(1)
        logger.info("爬虫任务执行完成")
    except Exception as e:
        logger.error(f"爬虫执行过程中发生异常: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main() 