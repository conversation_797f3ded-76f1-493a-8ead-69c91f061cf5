#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
huawei爬虫API模块
处理huawei爬虫系统的API调用
"""
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from spiders.base import BaseObject
from spiders.base import ConfigManager
from spiders.base.utils.http import RequestsManager
from spiders.huawei.dao import HuaweiOrder, HuaweiOrderDAO


class HuaweiAPI(BaseObject):
    """huawei爬虫API客户端"""

    MODULE_NAME = 'huawei'

    def __init__(self, config: ConfigManager):
        """
        初始化API客户端
        Args:
            config: 配置管理器
        """
        super().__init__(config)

        # API配置
        self.order_list_url = self._get_config('huawei.api.order_list_url')
        self.order_detail_url = self._get_config('huawei.api.order_detail_url')
        self.receive_url = self._get_config('huawei.api.receive_url')
        self.assign_url = self._get_config('huawei.api.assign_url')
        self.customer_service_url = self._get_config('huawei.api.customer_service_url')
        # 初始化 DAO
        self.dao = HuaweiOrderDAO(config)

    def fetch_orders(self,
                    auth_info: Dict,
                    status_list: Optional[List[str]] = None,
                    start_time: Optional[datetime] = None,
                    end_time: Optional[datetime] = None,
                    page: int = 1,
                    limit: int = 100) -> List[Dict]:
        """
        获取工单列表
        
        Args:
            auth_info: 认证信息
            status_list: 工单状态列表，默认为 ["待领取"]
            start_time: 开始时间，默认为半年前
            end_time: 结束时间，默认为当前时间
            page: 页码，默认为1
            limit: 每页数量，默认为10
            
        Returns:
            List[Dict]: 工单数据列表
        """
        # 设置默认值
        if status_list is None:
            status_list = ["待领取"]
            
        if start_time is None:
            start_time = datetime.now() - timedelta(days=180)  # 半年前
            
        if end_time is None:
            end_time = datetime.now()
            
        # 格式化时间
        start_time_str = start_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        end_time_str = end_time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
        
        with RequestsManager(self.config, spider_name=self.MODULE_NAME) as requests:
            data = {
                "limit": limit,
                "page": page,
                "searchCondition": {
                    "spId": auth_info.get("sp_id",  ""),
                    "statusList": status_list,
                    "orderTimeStartTime": start_time_str,
                    "orderTimeEndTime": end_time_str,
                    "tabType": "manage",
                    "businessCategory": "B_CHARGING"
                }
            }
            data = json.dumps(data, separators=(',', ':'))
            requests.load_session_state(auth_info)
            requests.session.headers.update({
                "Content-Type": "application/json;charset=UTF-8"
            })
            response = requests.post(self.order_list_url, data=data)
            if response.status_code != 200:
                self.logger.error(f"获取工单列表失败，状态码: {response.status_code}")
                return []
                
            response_data = response.json()
            if response_data.get("code") != 200:
                self.logger.error(f"获取工单列表失败，错误码: {response_data.get('code')}")
                return []
                
            items = response_data.get("data", {}).get("content", [])
            if not items:
                self.logger.info("没有获取到工单数据")
            else:
                self.logger.info(f"成功获取 {len(items)} 条工单数据")
                
            return items

    def accept_orders(self, auth_info: Dict, orders: List[HuaweiOrder]) -> List[HuaweiOrder]:
        """
        接受工单并返回成功列表
        
        Args:
            auth_info: 认证信息
            orders: 待接受的工单列表
            
        Returns:
            List[HuaweiOrder]: 接受成功的工单列表
        """
        successful_orders = []

        with RequestsManager(self.config, spider_name=self.MODULE_NAME) as requests:
            requests.load_session_state(auth_info)
            requests.session.headers.update({
                "Content-Type": "application/json;charset=UTF-8"
            })
            for order in orders:
                try:
                    data = {"projectCode":order.company_order_no,"operateType":"receiveOrder"}

                    if self.config.get_env() != 'prod':
                        self.logger.warning("当前环境非生产环境，跳过领单")
                        continue

                    data = json.dumps(data, separators=(',', ':'))
                    response = requests.post(self.receive_url, data=data)
                    if response.status_code == 200:
                        # 更新订单状态为接受成功
                        self.dao.update_order(company_order_no=order.company_order_no, status=2)
                        successful_orders.append(order)
                        self.logger.debug(f"✅ 工单接受成功: {order.company_order_no}")
                    else:
                        error_msg = f"工单接受失败[{response.status_code}]: {order.company_order_no}"
                        self.logger.error(error_msg)
                except Exception as e:
                    error_msg = f"工单接受异常: {order.company_order_no} | 错误: {str(e)}"
                    self.logger.error(error_msg, exc_info=True)
            total = len(successful_orders)
            self.logger.info(f"📥 工单接受完成，成功 {total}/{len(orders)} 条")
            return successful_orders

    def assigned_orders(self, auth_info: Dict, items: List[Dict]) -> List[HuaweiOrder]:
        """
        指派工单并返回成功列表
        
        Args:
            auth_info: 认证信息
            items: 待指派的工单列表

        Returns:
            List[HuaweiOrder]: 指派成功的工单列表
        """

        successful_orders = []
        with RequestsManager(self.config, spider_name=self.MODULE_NAME) as requests:
            requests.load_session_state(auth_info)
            requests.session.headers.update({
                "Content-Type": "application/json;charset=UTF-8"
            })
            for item in items:
                try:
                    assignee = item.get("assignee")
                    if not assignee:
                        self.logger.error(f"指派工单失败: {item.get('projectCode')} | 错误: 指派人员为空")
                        continue
                    customers = self.get_customer_service(auth_info=auth_info, project_code=item.get("projectCode"))
                    # 匹配customer中name包含assignee的customer
                    customer = next((c for c in customers if assignee in c.get("name", "")), None)
                    if not customer:
                        self.logger.error(f"指派工单失败: {item.get('projectCode')} | 错误: 指派人员={assignee}未找到")
                        continue

                    data = {
                        "projectCode": item.get('projectCode'),
                        "operateType": "assign",
                        "accountId": customer.get("accountId"),
                        "saasId": customer.get("saasId"),
                        "name": customer.get("name")
                    }

                    if self.config.get_env() != 'prod':
                        self.logger.warning("当前环境非生产环境，跳过指派工单")
                        continue

                    data = json.dumps(data, separators=(',', ':'))
                    response = requests.post(self.assign_url, data=data)
                    if response.status_code == 200:
                        # 更新订单状态为指派成功
                        order = self.dao.get_order_by_company_order_no(item.get("projectCode"))
                        if not order:
                            self.logger.error(f"✅ 工单指派成功: {item.get('projectCode')},但在数据库中未找到该工单")
                            continue
                        order.status = 3
                        self.dao.update_order(company_order_no=order.company_order_no, assigned_name=assignee, status=3)
                        successful_orders.append(order)
                        self.logger.info(f"✅ 工单指派成功: {order.company_order_no}")
                    else:
                        error_msg = f"工单指派失败[{response.status_code}]: {item.get('projectCode')}"
                        self.logger.error(error_msg)
                except Exception as e:
                    error_msg = f"工单指派异常: {item.get('projectCode')} | 错误: {str(e)}"
                    self.logger.error(error_msg, exc_info=True)
            self.logger.info(f"📥 工单指派完成，成功 {len(successful_orders)}/{len(items)} 条")
            return successful_orders

    def get_customer_service(self, auth_info: Dict, project_code: str) -> List[Dict]:
        """
        获取指派人员列表

        Args:
            auth_info: 认证信息

        Returns:
            List[Dict]: 指派人员列表
        """

        with RequestsManager(self.config, spider_name=self.MODULE_NAME) as requests:
            data = {
                "projectCode": project_code,
                "objectType": "SP",
                "spId": auth_info.get("sp_id",  "")
            }
            data = json.dumps(data, separators=(',', ':'))
            requests.load_session_state(auth_info)
            requests.session.headers.update({
                "Content-Type": "application/json;charset=UTF-8"
            })
            response = requests.post(self.customer_service_url, data=data)
            if response.status_code != 200:
                self.logger.error(f"获取指派人员列表失败，状态码: {response.status_code}")
                return []

            response_data = response.json()
            if response_data.get("code") != 200:
                self.logger.error(f"获取指派人员列表失败，错误码: {response_data.get('code')}")
                return []

            customers = response_data.get("data", [])
            if not customers:
                self.logger.info("没有获取到指派人员数据")
            else:
                self.logger.info(f"成功获取 {len(customers)} 条指派人员数据")

            return customers
