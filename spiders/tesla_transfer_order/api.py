#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉维修工单API模块
处理特斯拉维修工单系统的API调用
"""
import re
from typing import Dict, Optional, Any

import requests

from spiders.base import BaseObject
from spiders.base import ConfigManager


class TeslaRepairAPI(BaseObject):
    """特斯拉合作伙伴门户API客户端"""

    MODULE_NAME = 'tesla_repair'

    def __init__(self, config: ConfigManager):
        """
        初始化API客户端

        Args:
            config: 配置管理器
        """
        super().__init__(config)

        # API配置
        self.order_list_url = self._get_config('tesla_pp.order_list_url')
        self.order_detail_url = self._get_config('tesla_pp.order_detail_url')
        self.order_task_detail_url = self._get_config('tesla_pp.order_task_detail_url')
        self.order_task_accept_url = self._get_config('tesla_pp.order_task_accept_url')
        self.timeout = self._get_config('tesla_pp.fetch.timeout', 60)
        self.page_size = self._get_config('tesla_pp.fetch.page_size', 100)

    def search_repair_order(self, auth_info: Dict, order_no: str) -> Optional[Dict]:
        """
        通过工单号搜索维修工单

        Args:
            order_no: 工单号

        Returns:
            Optional[Dict]: 工单信息
        """
        try:
            self.logger.info(f"搜索维修工单: {order_no}")

            # 构建搜索参数
            params = {
                "pageIndex": "0",
                "pageSize": f"{self.page_size}"
            }

            # 如果单号已CD-开头，则去除CD-,用三元运算符判断是否CD-开头，如果是，则去除CD-,否则返回order_no表达式
            order_no_query = order_no if not order_no.startswith("CD-") else order_no[3:]

            # 构建搜索负载
            payload = self._build_order_payload({"Name":f"{order_no_query}","Source": "grid","Tab":"TaskOpen","SortActive":"id","SortDirection":"desc"})
            # payload = self._build_order_payload({"Name": f"{order_no}", "Source": "grid","Tab":"Progress"})

            # 发送请求
            response = requests.post(
                self.order_list_url,
                params=params,
                headers=self._build_headers(auth_info),
                json=payload,
                timeout=self.timeout
            )

            # 检查响应
            if response.status_code != 200:
                self.logger.error(f"搜索工单失败: HTTP {response.status_code}")
                raise Exception(f"搜索工单失败: HTTP {response.status_code}")

            # 解析响应
            data = response.json()
            items = data.get("Items", [])

            if not items:
                self.logger.warning(f"未找到工单: {order_no}")
                return None

            # 匹配order_no == item.Name的工单
            for item in items:
                if item.get("Name") == order_no:
                    order = item
                    self.logger.info(f"找到工单: {order.get('Name')}")
                    return order
            else:
                self.logger.warning(f"未找到工单: {order_no}")
                return None
        except Exception as e:
            self.logger.error(f"搜索工单异常: {str(e)}")
            raise e

    def get_order_detail(self, auth_info: Dict, item: Dict) -> Dict:
        """
        获取工单详情

        Args:
            auth_info: 认证信息
            item: 工单基本信息

        Returns:
            Dict: 包含详情的工单信息
        """
        try:
            item_id = item.get("Id")
            account_id = item.get("AccountId")

            if not (item_id and account_id):
                self.logger.warning(f"工单信息不完整，无法获取详情: {item.get('Name')}")
                return item

            self.logger.info(f"获取工单详情: {item.get('Name')}")

            # 构建请求URL
            url = f"{self.order_detail_url}/{item_id}/{account_id}"

            detail_url = f"https://partners.tesla.cn/home/<USER>/{item_id}/{account_id}"
            item["detail_url"] = detail_url

            # 发送请求
            response = requests.get(
                url,
                headers=self._build_headers(auth_info),
                timeout=self.timeout
            )

            # 检查响应
            if response.status_code != 200:
                self.logger.error(f"获取工单详情失败: HTTP {response.status_code}")
                return item

            # 解析响应
            data = response.json()

            item['IsPreCheck'] = '否'
            item['OrderType'] = '邮件转单'

            # 提取套包类型
            bill_of_materials = data.get("BillOfMaterials", [])
            vehicle_information = data.get("VehicleInformation", {})
            # 如果bill_of_materials不为空，则遍历bill_of_materials，获取第一个Description，返回字符串
            if bill_of_materials:
                first_item = bill_of_materials[0]
                description = first_item.get("Description")
                if description:
                    item["SuiteType"] = description
                    self.logger.info(f"套包类型: {description}")
            if vehicle_information:
                item['TslCarModel'] = vehicle_information.get("Model")

            # 套包类型处理
            suite_type = item.get("SuiteType")
            template_id = 709
            if suite_type:
                # 匹配40米或40 米
                if re.search(r'40\s*米', suite_type.lower()):
                    if "cybervault" in suite_type.lower():
                        template_id = 745
                    else:
                        template_id = 799

                elif any(keyword in suite_type.lower() for keyword in ["0米", "含勘测服务"]):
                    province_name = item.get("InstallationState", '')
                    if "新疆" in province_name.lower():
                        template_id = 731
                    elif any(keyword in province_name.lower() for keyword in ["云南", "甘肃", "宁夏"]):
                        template_id = 730
                    elif any(keyword in province_name.lower() for keyword in ["上海", "江苏", "浙江"]):
                        template_id = 703
                    else:
                        template_id = 709
                elif "家庭充电桩移桩服务" in suite_type.lower():
                    self.logger.error(f"套包类型为移桩服务，跳过: {suite_type}")
                    return item
            item['TemplateId'] = template_id
            return item

        except Exception as e:
            self.logger.error(f"获取工单详情异常: {str(e)}")
            return item

    @staticmethod
    def _build_order_payload(overrides: Optional[Dict] = None) -> Dict[str, Any]:
        """
        构建工单请求负载

        Args:
            overrides: 重写字段

        Returns:
            Dict: 请求负载
        """
        payload = {
            "FirstName": None,
            "LastName": None,
            "Name": None,
            "InstallationPostalCode": None,
            "InstallationCity": None,
            "InstallationCountry": None,
            "InstallationRegion": None,
            "BidStatus": None,
            "InstallationStatus": None,
            "Status": None,
            "Tab": "New",
            "SoProduct": None,
            "SortActive": "id",
            "SortDirection": "desc",
            "State": None,
            "QuoteExpirationDateRange": None,
            "SharedDateRange": None,
            "ScheduledDeliveryDateRange": None,
            "Rn": None,
            "AccountId": None,
            "Source": "repair",
            "FoStatus": None,
            "CustomerPhoneNumber": None,
            "CustomerEmail": None,
            "InitialContactDateRange": None,
            "SiteVisitDateRange": None,
            "InstallationDateRange": None,
            "LastUpdateDateRange": None,
            "OrderNumber": None,
            "ShippingDateRange": None,
            "ShippingTrackingNumber": None,
            "SharedStartDate": None,
            "SharedEndDate": None,
            "taskQueue": [11230]
        }

        # 应用重写字段
        if overrides:
            valid_keys = payload.keys()
            filtered_updates = {k: v for k, v in overrides.items() if k in valid_keys}
            payload.update(filtered_updates)

        return payload

    @staticmethod
    def _build_headers(auth_info: Dict) -> Dict:
        """构建请求头"""
        headers = {
            "Host": "akamai-apigateway-partnerportalcn.tesla.cn",
            "Connection": "keep-alive",
            "sec-ch-ua-platform": "\"macOS\"",
            "sec-ch-ua": "\"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"",
            "sec-ch-ua-mobile": "?0",
            "Accept": "application/json",
            "channel": "web",
            "account-country": "CN",
            "Origin": "https://partners.tesla.cn",
            "Sec-Fetch-Site": "same-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://partners.tesla.cn/",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Content-Type": "application/json"
        }

        # 添加认证信息
        if auth_info:
            headers["Authorization"] = f"Bearer {auth_info.get('access_token')}"
            headers["idtoken"] = auth_info.get('id_token')

        return headers