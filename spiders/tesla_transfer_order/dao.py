#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉转单工单邮件数据访问模块
负责邮件和转单工单数据的存储和访问
"""
import json
from typing import List, Optional

from sqlalchemy import Column, String, Integer, DateTime, BigInteger, func, create_engine, Text, Boolean
from sqlalchemy.orm import declarative_base, sessionmaker

from spiders.base import BaseObject
from spiders.base import ConfigManager

Base = declarative_base()


class TslEmail(Base):
    """特斯拉邮件表，存储所有扫描到的特斯拉邮件"""
    __tablename__ = 'tsl_email'

    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    email_id = Column(String(100), nullable=False, unique=True, comment='邮件ID')
    sender = Column(String(100), nullable=False, comment='发件人')
    subject = Column(String(255), nullable=False, comment='邮件主题')
    receive_date = Column(DateTime, nullable=False, comment='接收日期')
    content_type = Column(String(50), comment='内容类型：text/html/attachment')
    has_attachment = Column(Boolean, default=False, comment='是否有附件')
    attachment_names = Column(Text, comment='附件名称列表(JSON)')
    
    # 附件存储路径，如果有多个附件，以JSON数组形式存储
    attachment_paths = Column(Text, comment='附件路径列表(JSON)')
    # 邮件内容
    content = Column(Text, comment='邮件内容')
    # 处理状态：0-未处理，1-处理中，2-处理完成，3-处理失败
    status = Column(Integer, default=0, comment='处理状态：0-未处理，1-处理中，2-处理完成，3-处理失败')
    # 处理次数
    retry_count = Column(Integer, default=0, comment='重试次数')
    # 处理信息
    process_info = Column(Text, comment='处理信息')
    extract_orders = Column(Text, comment='提取的工单信息(JSON)')
    create_time = Column(DateTime, nullable=False, server_default=func.current_timestamp(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, server_default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='更新时间')

class TslEmailDAO(BaseObject):
    """特斯拉维修工单邮件数据访问对象"""
    
    MODULE_NAME = 'tesla_transfer_order'
    
    def __init__(self, config: ConfigManager):
        """初始化DAO对象"""
        super().__init__(config)
        self.engine = create_engine(config.get('database.url'), pool_recycle=1800)
        self.Session = sessionmaker(bind=self.engine)
        
    def create_tables(self):
        """创建所有表结构"""
        Base.metadata.create_all(self.engine)
        self.logger.info("创建特斯拉维修工单邮件相关表结构完成")
    
    # 邮件相关方法
    def save_email(self, email: TslEmail) -> Optional[TslEmail]:
        """保存邮件记录，如果已存在则返回现有记录"""
        session = self.Session()
        try:
            # 检查邮件是否已存在
            existing_email = session.query(TslEmail).filter(
                TslEmail.email_id == email.email_id
            ).first()
            
            if existing_email:
                self.logger.info(f"邮件已存在: {email.email_id}")
                return None
            
            # 保存新邮件
            session.add(email)
            session.commit()
            session.refresh(email)
            return email
        except Exception as e:
            session.rollback()
            self.logger.error(f"保存邮件失败: {str(e)}")
            return None
        finally:
            session.close()

    def get_email_by_id(self, email_id: str) -> Optional[TslEmail]:
        """
        根据邮件ID查询邮件记录

        Args:
            email_id: 邮件ID

        Returns:
            Optional[TslEmail]: 邮件对象，如果找不到则返回None
        """
        session = self.Session()
        try:
            email = session.query(TslEmail).filter(
                TslEmail.email_id == email_id
            ).first()
            return email
        except Exception as e:
            self.logger.error(f"查询邮件记录失败: {str(e)}")
            return None
        finally:
            session.close()
    
    def get_emails_by_status(self, status: int, limit: int = 50) -> Optional[List[TslEmail]]:
        """
        根据状态查询邮件列表

        Args:
            status: 状态码
            limit: 查询数量限制

        Returns:
            List[TslEmail]: 邮件列表
        """
        session = self.Session()
        try:
            return session.query(TslEmail).filter(TslEmail.status == status).order_by(TslEmail.receive_date).limit(limit).all()
        except Exception as e:
            self.logger.error(f"获取邮件失败: {str(e)}")
            return []
        finally:
            session.close()
    
    def update_email_status(self, email_id: str, status: int, process_info: str = None, order_nos: List[str] = None,retry_count: int = 0) -> bool:
        """
        更新邮件状态
        
        Args:
            email_id: 邮件ID
            status: 新状态码
            process_info: 备注信息
            order_numbers: 提取的工单号列表（可选）
            
        Returns:
            bool: 更新是否成功
        """
        session = self.Session()
        try:
            email = session.query(TslEmail).filter(TslEmail.email_id == email_id).first()
            if not email:
                self.logger.error(f"邮件不存在: {email_id}")
                return False
            
            email.status = status
            if process_info:
                email.process_info = process_info
            if order_nos:
                email.extract_orders = json.dumps(order_nos)
            if retry_count:
                email.retry_count = retry_count
            
            session.commit()
            return True
        except Exception as e:
            session.rollback()
            self.logger.error(f"更新邮件 {email_id} 状态异常: {str(e)}", exc_info=True)
            return False
        finally:
            session.close()