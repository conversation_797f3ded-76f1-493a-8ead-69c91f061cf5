#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉转单工单认证处理器
负责获取特斯拉API的认证信息
"""
import json
import random
import re
from datetime import datetime, timedelta
from typing import Dict, Optional
from urllib.parse import urlparse

from spiders.base import ConfigManager
from spiders.base.processors.base_auth_processor import BaseAuthProcessor
from spiders.base.utils.browser import BrowserManager
from spiders.base.utils.human import HumanBehaviorSimulator


class AuthProcessor(BaseAuthProcessor):
    """
    认证处理器，负责获取特斯拉API的认证信息
    """
    MODULE_NAME = 'tesla_transfer_order'
    PROCESS_NAME = '认证处理器'
    
    def __init__(self, config: ConfigManager):
        """
        初始化处理器
        
        Args:
            config: 配置管理器
        """
        super().__init__(config)
        
        # 登录配置
        self.login_url = self._get_config('login.url')
        self.username = self._get_config('login.username')
        self.password = self._get_config('login.password')
        self.max_login_retries = self._get_config('login.max_login_retries', 3)
        
        # 浏览器管理器
        self.browser_manager = BrowserManager(config)
        
        # 人类行为模拟器
        self.human = HumanBehaviorSimulator()

        # 鼠标初始位置
        self.current_mouse = (100, 200)
    
    def load_auth_info(self) -> Optional[Dict]:
        """加载认证信息"""
        try:
            with open(self.auth_file, 'r') as f:
                state_data = json.load(f)
                target_domain = urlparse("https://partners.tesla.cn").netloc
                for origin in state_data.get('origins', []):
                    current_domain = urlparse(origin['origin']).netloc
                    if current_domain == target_domain:
                        storage =  {item['name']: item['value'] for item in origin.get('localStorage', [])}
                        """解析认证数据"""
                        auth_json = storage.get('partner-leadsharing:token', '{}')
                        auth_data = json.loads(auth_json) or {}

                        issued_at = datetime.fromtimestamp(auth_data.get('issuedAt', 0))
                        return {
                            'access_token': auth_data.get('accessToken', ''),
                            'token_type': auth_data.get('tokenType', ''),
                            'expires_in': auth_data.get('expiresIn', 0),
                            'issued_at': issued_at,
                            'expires_at': issued_at + timedelta(seconds=auth_data.get('expiresIn', 0)),
                            'id_token': auth_data.get('idToken', ''),
                            'raw_data': auth_data
                        }
            return {}
        except FileNotFoundError:
            self.logger.error("本地认证信息文件不存在")
            return {}
        except json.JSONDecodeError:
            self.logger.error("本地存储文件解析失败")
            return {}
        except Exception as e:
            self.logger.error(f"本地认证信息数据提取异常: {str(e)}")
            return {}
    
    def is_valid(self, auth_info: Dict) -> bool:
        """
        检查认证信息是否有效
        
        Args:
            auth_info: 认证信息
            
        Returns:
            bool: 认证信息是否有效
        """
        # 检查令牌是否过期
        if 'expires_at' in auth_info:
            return datetime.now() < auth_info.get('expires_at', datetime.min) - timedelta(hours=1)
        return False
    
    def login(self):
        """
        执行登录过程，获取认证信息
        
        Returns:
            Optional[Dict]: 认证信息字典，如果登录失败则返回None
        """
        self.logger.info("开始特斯拉工单系统登录")
        try:
            with BrowserManager(self.config) as context:
                page = context.new_page()
                """导航到登录页"""
                page.goto(f"{self.login_url}?r={random.randint(1000, 9999)}")
                self.human.random_scroll(page)
                self.human.random_delay(1000, 2000)

                """处理用户名输入"""
                username_field = page.wait_for_selector("#form-input-identity", timeout=10000)
                self.current_mouse = self.human.realistic_click(page, username_field, self.current_mouse)
                self.human.realistic_type(username_field, self.username)

                """点击下一步|Next"""
                next_btn = page.get_by_role("button", name=re.compile(r"下一步|Next"))
                self.current_mouse = self.human.realistic_click(page, next_btn, self.current_mouse)

                """处理密码输入"""
                password_field = page.wait_for_selector("#form-input-credential", timeout=10000)
                if not password_field:
                    raise Exception("未找到密码输入框")

                self.current_mouse = self.human.realistic_click(page, password_field, self.current_mouse)
                self.human.realistic_type(password_field, self.password)

                login_btn = page.get_by_role("button", name=re.compile(r"登录|Sign In"))
                self.current_mouse = self.human.realistic_click(page, login_btn, self.current_mouse)

                # 等待登录完成
                self.logger.info("等待登录完成")

                # 检查是否登录成功
                # 等待重定向或者Dashboard页面加载
                page.wait_for_load_state('networkidle', timeout=20000)

                # 检查页面URL或特定元素，判断是否登录成功
                self.logger.info(f"当前页面URL: {page.url}")

                self.human.random_scroll(page)
                self.human.random_delay(2000, 10000)

                # 登录成功，获取登录信息
                self.logger.info("登录成功")

                HumanBehaviorSimulator.random_delay(1000, 2000)
                context.storage_state(path=self.auth_file)
                self.logger.info("✅ 浏览器状态已保存")
                return True
        except Exception as e:
            if self.browser_manager.enable_tracing:
                trace_path = "traces/error_trace.zip"
                context.tracing.stop(path=trace_path)
            self.logger.error(f"登录失败: {str(e)}")
            return False

    def show_auth_info(self, auth_info: Dict) -> None:
        """显示认证信息"""
        self.logger.info("🔐 认证信息摘要：")
        self.logger.info(f"• 令牌类型: {auth_info['token_type']}")
        self.logger.info(f"• 签发时间: {auth_info['issued_at'].strftime('%Y-%m-%d %H:%M:%S')}")
        self.logger.info(f"• 有效时长: {auth_info['expires_in'] // 3600} 小时")
        self.logger.info(f"• 过期时间: {auth_info['expires_at'].strftime('%Y-%m-%d %H:%M:%S')}")
