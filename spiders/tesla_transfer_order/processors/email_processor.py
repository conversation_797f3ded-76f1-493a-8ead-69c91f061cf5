#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉转单邮件处理器
负责从邮件中提取工单号信息并更新邮件状态
"""
import json
import re
from typing import Dict, Any, List

from bs4 import BeautifulSoup

from spiders.base.utils.logger import ProgressLogger
from spiders.framework import BaseProcessor, Status
from spiders.tesla_transfer_order.dao import TslEmailDAO, TslEmail


class EmailProcessor(BaseProcessor):
    """
    邮件处理器，负责从邮件中提取工单号信息并更新邮件状态
    """

    MODULE_NAME = 'tesla_transfer_order'
    PROCESS_NAME = '邮件处理器'

    def __init__(self, config):
        """
        初始化处理器

        Args:
            config: 配置管理器
        """
        super().__init__(config)

        self.dao = TslEmailDAO(config)

    def process(self, context: Dict[str, Any]) -> Status:
        """
        执行邮件处理逻辑

        Args:
            context: 处理上下文

        Returns:
            Status: 处理结果状态
        """
        try:
            # 初始化子流程状态
            process_status = {
                "获取邮件": {"status": "未开始", "message": ""},
                "提取工单号": {"status": "未开始", "message": ""},
                "更新邮件状态": {"status": "未开始", "message": ""}
            }

            self.logger.info("=" * 80)
            self.logger.info("📧 邮件处理器开始执行")
            self.logger.info("=" * 80)

            # 1. 获取未处理的邮件（状态为0的邮件）
            self.logger.info("🔍 正在获取未处理的邮件")
            process_status["获取邮件"]["status"] = "进行中"

            emails = self.dao.get_emails_by_status(0)

            if not emails:
                self.logger.info("ℹ️ 没有需要处理的邮件")
                process_status["获取邮件"]["status"] = "完成"
                process_status["获取邮件"]["message"] = "没有未处理邮件"

                # 打印子流程状态摘要
                self._print_process_status_summary(process_status)
                return Status(True, "没有需要处理的邮件")

            self.logger.info(f"📋 找到 {len(emails)} 封未处理邮件")
            process_status["获取邮件"]["status"] = "成功"
            process_status["获取邮件"]["message"] = f"{len(emails)} 封未处理邮件"

            # 2. 开始提取工单号
            self.logger.info("🔄 开始处理邮件并提取工单号")
            process_status["提取工单号"]["status"] = "进行中"

            processed_count = 0
            success_count = 0
            failed_count = 0
            processed_emails = []

            for email_obj in emails:
                processed_count += 1

                # 使用ProgressLogger输出进度条
                ProgressLogger.log_progress(logger=self.logger,current=processed_count,total=len(emails),prefix="提取邮件中的工单",emoji="📩")

                try:
                    # 提取工单号
                    self.logger.info(f"🔎 从邮件中提取工单号: {email_obj.subject[:30]}{'...' if len(email_obj.subject) > 30 else ''}")
                    order_numbers = self._extract_order_numbers(email_obj)

                    if not order_numbers:
                        self.logger.info(f"ℹ️ 邮件 {email_obj.subject[:30]}{'...' if len(email_obj.subject) > 30 else ''} 未提取到工单号")
                        self.dao.update_email_status(email_obj.email_id, 1, "未提取到工单号", [])
                        continue

                    self.logger.info(f"✅ 邮件 {email_obj.subject[:30]}{'...' if len(email_obj.subject) > 30 else ''} 提取到 {len(order_numbers)} 个工单号")

                    # 更新邮件状态为处理完成，并保存提取的工单号
                    self.dao.update_email_status(
                        email_obj.email_id,
                        1,
                        f"成功提取 {len(order_numbers)} 个工单号",
                        order_numbers
                    )

                    # 记录成功处理的邮件
                    email_obj.extract_orders = json.dumps(order_numbers)
                    processed_emails.append(email_obj)
                    success_count += 1

                except Exception as e:
                    self.logger.error(f"❌ 处理邮件 {email_obj.email_id} 时发生错误: {str(e)}")
                    failed_count += 1

            # 处理完成
            process_status["提取工单号"]["status"] = "成功"
            process_status["提取工单号"]["message"] = f"成功处理 {success_count}/{len(emails)} 封邮件"

            process_status["更新邮件状态"]["status"] = "成功"
            process_status["更新邮件状态"]["message"] = f"成功: {success_count}, 失败: {failed_count}"

            # 打印子流程状态摘要
            self._print_process_status_summary(process_status)

            # 将处理的邮件添加到上下文
            context['processed_emails'] = processed_emails

            self.logger.info("=" * 80)
            self.logger.info("✅ 邮件处理器执行完成")
            self.logger.info("=" * 80)

            return Status(
                True,
                f"邮件处理完成，成功提取 {success_count}/{len(emails)} 封邮件的工单号",
                {"total_emails": len(emails), "success_count": success_count, "failed_count": failed_count}
            )

        except Exception as e:
            self.logger.error(f"❌ 邮件处理过程出现异常: {str(e)}", exc_info=True)
            return Status(False, f"邮件处理异常: {str(e)}")

    def _extract_order_numbers(self, email_obj: TslEmail) -> List[str]:
        """
        从邮件中提取维修工单号

        Args:
            email_obj: 邮件对象

        Returns:
            List[str]: 工单号列表
        """
        order_numbers = []

        try:
            # 首先从邮件标题提取工单号（最高优先级）
            if email_obj.subject:
                self.logger.info(f"🔎 从邮件标题中提取工单号: {email_obj.subject}")
                title_order_numbers = self._extract_order_numbers_from_title(email_obj.subject)
                if title_order_numbers:
                    order_numbers.extend(title_order_numbers)
                    self.logger.info(f"✅ 从邮件标题中提取到 {len(title_order_numbers)} 个工单号")

            # 然后从邮件内容中提取工单号
            if email_obj.content:
                content = email_obj.content
                # 根据内容类型选择不同的解析方法
                if email_obj.content_type == 'html':
                    # 从HTML中提取工单号
                    order_numbers.extend(self._extract_order_numbers_from_html(content))
                else:
                    # 从纯文本中提取工单号
                    order_numbers.extend(self._extract_order_numbers_from_text(content))
            # 去重
            order_numbers = list(set(order_numbers))

            return order_numbers

        except Exception as e:
            self.logger.error(f"提取工单号时发生错误: {str(e)}")
            raise

    def _extract_order_numbers_from_title(self, title: str) -> List[str]:
        """
        从邮件标题中提取维修工单号

        Args:
            title: 邮件标题

        Returns:
            List[str]: 提取到的工单号列表
        """
        order_numbers = []

        try:
            # title内容类似 'Daomeijia-订单转入通知-JB-CN1002199019-00-河南省',请提取 JB-CN1002199019-00

            if "订单转入通知-" in title:
                # 获取"维修订单-"后面的所有内容
                order_part = title.split("订单转入通知-", 1)[1].strip()

                if order_part:
                    self.logger.info(f"从标题中提取到工单号: {order_part}")
                    order_numbers.append(order_part)

            return order_numbers

        except Exception as e:
            self.logger.error(f"从标题提取工单号失败: {str(e)}")
            return []

    def _extract_order_numbers_from_html(self, html_content: str) -> List[str]:
        """
        从HTML内容中提取维修工单号

        Args:
            html_content: HTML内容

        Returns:
            List[str]: 提取到的工单号列表
        """
        order_numbers = []

        try:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')

            # 移除script和style标签内容
            for script in soup(["script", "style"]):
                script.extract()

            # 获取纯文本
            text = soup.get_text()

            # 提取工单号
            order_numbers.extend(self._extract_order_numbers_from_text(text))

            return order_numbers

        except Exception as e:
            self.logger.error(f"从HTML提取工单号失败: {str(e)}")
            return []

    def _extract_order_numbers_from_text(self, text_content: str) -> List[str]:
        """
        从文本内容中提取工单号

        Args:
            text_content: 文本内容

        Returns:
            List[str]: 提取到的工单号列表
        """
        order_numbers = []

        try:
            # 使用正则表达式提取工单号
            patterns = [
                r'JB-CN\d{10}-\d{2}',  # JB-CN1000893347-00 格式
                r'INSTCN_\d{6}',       # INSTCN_936897 格式
                r'CD-\d{9}',           # CD-000085482 格式
                r'[A-Z0-9]{17}'        # VIN码格式，如LRW3E7FS3NC590651
            ]

            for pattern in patterns:
                matches = re.findall(pattern, text_content)
                order_numbers.extend(matches)

            # 增加对"JB "后面内容的直接提取
            # 匹配JB后跟一个空格，然后提取空格后面的所有非空白字符作为工单号
            jb_pattern = r'JB\s+(\S+)'
            jb_matches = re.findall(jb_pattern, text_content)
            if jb_matches:
                self.logger.info(f"从正文中直接提取JB空格后的工单号: {jb_matches}")
                for match in jb_matches:
                    # 如果工单号中包含标点符号如逗号、句号等在末尾，去除它们
                    clean_match = re.sub(r'[,\.;:\?\!]$', '', match)
                    order_numbers.append(clean_match)

            return order_numbers

        except Exception as e:
            self.logger.error(f"从文本提取工单号失败: {str(e)}")
            return []