#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉转单工单邮件扫描处理器
负责连接邮箱服务器，扫描和保存邮件
"""
import base64
import email
import imaplib
import json
import socket
import ssl
import time
from datetime import datetime, timedelta
from email.header import decode_header
from typing import Dict, Any, List, Tuple, Optional

from spiders.base.utils.logger import ProgressLogger
from spiders.framework import BaseProcessor, Status
from spiders.tesla_transfer_order.dao import TslEmail, TslEmailDAO

class EmailScanner(BaseProcessor):
    """
    邮件扫描处理器，负责连接邮箱服务器，扫描和保存邮件
    """

    MODULE_NAME = 'tesla_transfer_order'
    PROCESS_NAME = '邮件扫描处理器'

    def __init__(self, config):
        """
        初始化处理器

        Args:
            config: 配置管理器
        """
        super().__init__(config)

        # 邮箱配置
        self.imap_server = self._get_config('email.imap_server')
        self.imap_port = self._get_config('email.imap_port', 993)
        self.imap_ssl = self._get_config('email.imap_ssl', True)
        self.username = self._get_config('email.username')
        self.password = self._get_config('email.password')
        self.scan_folder = self._get_config('email.scan_folder', 'INBOX')
        self.sender_filter = self._get_config('email.sender_filter', '')
        self.subject_filter = self._get_config('email.subject_filter', '')
        self.days_back = self._get_config('email.days_back', 1)

        # 数据访问对象
        self.dao = TslEmailDAO(config)

    def process(self, context: Dict[str, Any]) -> Status:
        """
        执行邮件扫描处理逻辑

        Args:
            context: 处理上下文

        Returns:
            Status: 处理结果状态
        """
        try:
            # 初始化子流程状态
            scan_status = {
                "连接服务器": {"status": "未开始", "message": ""},
                "搜索邮件": {"status": "未开始", "message": ""},
                "处理邮件": {"status": "未开始", "message": ""}
            }

            self.logger.info("=" * 80)
            self.logger.info("📧 邮件扫描处理器开始执行")
            self.logger.info("=" * 80)

            # 1. 连接IMAP服务器
            self.logger.info(f"🔄 连接IMAP服务器: {self.imap_server}:{self.imap_port}")
            scan_status["连接服务器"]["status"] = "进行中"

            try:
                mail = self._connect_to_mail_server()
                self.logger.info(f"✅ 成功登录邮箱: {self.username}")
                scan_status["连接服务器"]["status"] = "成功"
            except Exception as conn_err:
                self.logger.error(f"❌ 连接IMAP服务器失败: {str(conn_err)}")
                scan_status["连接服务器"]["status"] = "失败"
                scan_status["连接服务器"]["message"] = f"连接异常: {str(conn_err)}"
                return Status(False, f"连接邮箱服务器失败: {str(conn_err)}")

            # 2. 构建搜索条件和执行搜索
            self.logger.info("🔍 构建邮件搜索条件")
            scan_status["搜索邮件"]["status"] = "进行中"

            try:
                email_ids = self._search_emails(mail)

                if not email_ids:
                    self.logger.info("ℹ️ 未找到符合条件的邮件")
                    scan_status["搜索邮件"]["status"] = "完成"
                    scan_status["搜索邮件"]["message"] = "未找到符合条件的邮件"

                    # 关闭连接
                    self._close_connection(mail)

                    # 打印子流程状态摘要
                    self._print_process_status_summary(scan_status)
                    return Status(True, "未找到符合条件的邮件")

                self.logger.info(f"📨 找到 {len(email_ids)} 封符合条件的邮件")
                scan_status["搜索邮件"]["status"] = "成功"
                scan_status["搜索邮件"]["message"] = f"找到 {len(email_ids)} 封邮件"
            except Exception as search_err:
                self.logger.error(f"❌ 执行邮件搜索时出错: {str(search_err)}")
                scan_status["搜索邮件"]["status"] = "失败"
                scan_status["搜索邮件"]["message"] = f"搜索异常: {str(search_err)}"

                # 关闭连接
                self._close_connection(mail)

                return Status(False, f"搜索邮件失败: {str(search_err)}")

            # 3. 处理每封邮件
            self.logger.info("📥 开始处理搜索到的邮件")
            scan_status["处理邮件"]["status"] = "进行中"

            try:
                # 串行处理邮件，复用同一个连接
                results = []
                total_emails = len(email_ids)

                # 使用进度条格式显示处理进度
                for idx, email_id in enumerate(email_ids, 1):
                    # # 计算进度百分比
                    # progress = int((idx / total_emails) * 100)
                    # # 创建进度条
                    # bar_length = 30
                    # filled_length = int(bar_length * idx // total_emails)
                    # bar = '█' * filled_length + '░' * (bar_length - filled_length)
                    # # 输出进度条
                    # self.logger.info(f"📩 处理邮件 [{bar}] {progress}% ({idx}/{total_emails}) ID:{email_id}")

                    # 使用ProgressLogger输出进度条
                    ProgressLogger.log_progress(
                        logger=self.logger,
                        current=idx,
                        total=total_emails,
                        prefix="扫描邮件",
                        emoji="📩"
                    )

                    try:
                        result = self._process_single_email(mail, email_id)
                        if result:
                            results.append(result)
                    except Exception as e:
                        self.logger.error(f"❌ 处理邮件 {email_id} 时发生错误: {str(e)}")

                    # 添加短暂延迟，避免服务器响应问题
                    time.sleep(0.1)

                new_email_count = len(results)
                context['new_emails'] = results

                # 处理完成
                if new_email_count > 0:
                    self.logger.info(f"✅ 成功处理并保存 {new_email_count} 封新邮件")
                    scan_status["处理邮件"]["status"] = "成功"
                    scan_status["处理邮件"]["message"] = f"保存 {new_email_count} 封新邮件"
                else:
                    self.logger.info("ℹ️ 没有新邮件需要保存")
                    scan_status["处理邮件"]["status"] = "完成"
                    scan_status["处理邮件"]["message"] = "没有新邮件"
            except Exception as proc_err:
                self.logger.error(f"❌ 处理邮件过程出错: {str(proc_err)}")
                scan_status["处理邮件"]["status"] = "失败"
                scan_status["处理邮件"]["message"] = f"处理异常: {str(proc_err)}"
                new_email_count = 0

            # 关闭连接
            self._close_connection(mail)

            # 打印子流程状态摘要
            self._print_process_status_summary(scan_status)

            self.logger.info("=" * 80)
            self.logger.info("✅ 邮件扫描处理器执行完成")
            self.logger.info("=" * 80)

            return Status(
                True,
                f"邮件扫描完成，新增 {new_email_count} 封邮件",
                {"new_email_count": new_email_count}
            )

        except Exception as e:
            self.logger.error(f"❌ 扫描邮件过程出现异常: {str(e)}", exc_info=True)
            return Status(False, f"扫描邮件异常: {str(e)}")

    def _connect_to_mail_server(self) -> imaplib.IMAP4:
        # 设置超时
        socket.setdefaulttimeout(10)  # 设置10秒超时

        # 添加重试逻辑
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 连接服务器
                if self.imap_ssl:
                    # 创建SSL上下文以优化连接速度
                    context = ssl.create_default_context()
                    context.check_hostname = False
                    context.verify_mode = ssl.CERT_NONE

                    mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port, ssl_context=context)
                else:
                    mail = imaplib.IMAP4(self.imap_server, self.imap_port)

                # 登录
                mail.login(self.username, self.password)

                # 发送IMAP ID信息（163邮箱要求）
                try:
                    # 使用原始socket发送ID命令
                    id_cmd = 'A001 ID ("name" "Tesla Transfer Order Spider" "version" "1.0.0" "vendor" "Tesla Spider" "support-email" "<EMAIL>")\r\n'
                    mail.sock.send(id_cmd.encode())

                    # 读取响应
                    response = mail.sock.recv(1024).decode()
                    self.logger.info(f"📋 IMAP ID响应: {response.strip()}")
                    self.logger.info("📋 已发送IMAP ID信息")
                except Exception as id_err:
                    self.logger.warning(f"⚠️ 发送IMAP ID信息失败: {id_err}")

                # 选择邮箱文件夹
                status, data = mail.select(self.scan_folder)
                if status != 'OK':
                    raise Exception(f"选择邮件文件夹失败: {status}, {data}")
                self.logger.info(f"📁 选择邮件文件夹: {self.scan_folder}")

                return mail
            except socket.timeout:
                retry_count += 1
                self.logger.warning(f"连接超时，正在尝试第{retry_count}次重试...")
                time.sleep(2)  # 等待2秒后重试
            except Exception as e:
                raise e
        raise ConnectionError(f"连接IMAP服务器失败，已重试{max_retries}次")

    def _search_emails(self, mail: imaplib.IMAP4) -> List[bytes]:
        """
        搜索符合条件的邮件

        Args:
            mail: 邮件服务器连接对象

        Returns:
            List[bytes]: 邮件ID列表

        Raises:
            Exception: 搜索失败时抛出异常
        """
        search_criteria = []

        # 添加日期过滤
        if self.days_back > 0:
            date_since = (datetime.now() - timedelta(days=self.days_back)).strftime("%d-%b-%Y")
            search_criteria.append(f'SINCE {date_since}')
            self.logger.info(f"📅 搜索日期条件: 从 {date_since} 开始")

        # 添加发件人过滤
        if self.sender_filter:
            search_criteria.append(f'FROM "{self.sender_filter}"')
            self.logger.info(f"👤 搜索发件人条件: {self.sender_filter}")

        # 主题过滤将在客户端进行，因为IMAP对中文支持有限
        if self.subject_filter:
            self.logger.info(f"📝 主题过滤条件: {self.subject_filter} (将在客户端过滤)")

        # 如果没有条件，搜索所有邮件
        search_query = " ".join(search_criteria) if search_criteria else "ALL"

        self.logger.info(f"🔎 最终搜索条件: {search_query}")

        # 执行搜索
        status, data = mail.search(None, search_query)

        if status != 'OK':
            raise Exception(f"搜索邮件失败: {status}")

        # 获取邮件ID列表
        return data[0].split() if data[0] else []

    def _process_single_email(self, mail: imaplib.IMAP4, email_id: bytes) -> Optional[TslEmail]:
        """
        处理单封邮件

        Args:
            mail: 邮件服务器连接对象
            email_id: 邮件ID

        Returns:
            Optional[TslEmail]: 处理成功返回邮件对象，否则返回None
        """
        try:
            # 获取邮件
            status, data = mail.fetch(email_id, '(RFC822)')

            if status != 'OK' or not data or not data[0]:
                self.logger.error(f"❌ 获取邮件 {email_id} 失败: {status}")
                return None

            # 检查获取的数据是否有效
            if not isinstance(data[0], tuple) or len(data[0]) < 2:
                self.logger.error(f"❌ 邮件数据格式异常: {data[0]}")
                return None

            # 解析邮件
            raw_email = data[0][1]
            if not raw_email:
                self.logger.error(f"❌ 邮件内容为空: {email_id}")
                return None

            msg = email.message_from_bytes(raw_email)

            # 获取邮件ID（使用Message-ID或生成唯一ID）
            msg_id = msg.get('Message-ID', f"{msg.get('From')}-{msg.get('Date')}-{email_id.decode()}")
            if not msg_id:
                msg_id = f"unknown-{datetime.now().strftime('%Y%m%d%H%M%S')}-{email_id.decode()}"

            # 获取发件人
            sender = self._decode_header_str(msg.get('From', ''))

            # 获取主题
            subject = self._decode_header_str(msg.get('Subject', ''))

            # 客户端主题过滤
            if self.subject_filter and self.subject_filter not in subject:
                self.logger.debug(f"🔍 邮件主题不匹配过滤条件，跳过: {subject[:50]}{'...' if len(subject) > 50 else ''}")
                return None

            # 获取接收日期
            date_str = msg.get('Date', '')
            try:
                # 尝试解析日期
                receive_date = email.utils.parsedate_to_datetime(date_str)
            except:
                # 如果解析失败，使用当前时间
                receive_date = datetime.now()

            # 检查邮件是否已存在
            existing_email = self.dao.get_email_by_id(msg_id)
            if existing_email:
                return None

            # 处理邮件内容和附件
            content, content_type, has_attachment, attachment_names, attachment_paths = self._process_email_content(msg)

            # 创建邮件对象
            email_obj = TslEmail(
                email_id=msg_id,
                sender=sender,
                subject=subject,
                receive_date=receive_date,
                content_type=content_type,
                has_attachment=has_attachment,
                attachment_names=json.dumps(attachment_names) if attachment_names else None,
                attachment_paths=json.dumps(attachment_paths) if attachment_paths else None,
                content=content,
                status=0  # 未处理
            )

            # 保存到数据库
            saved_email = self.dao.save_email(email_obj)

            if saved_email and saved_email.id:
                self.logger.debug(f"✅ 保存新邮件: {subject[:30]}{'...' if len(subject) > 30 else ''}")
                return saved_email

            return None

        except Exception as e:
            self.logger.error(f"❌ 处理邮件 {email_id} 异常: {str(e)}")
            return None

    def _decode_header_str(self, header_str: str) -> str:
        """
        解码邮件头部字符串

        Args:
            header_str: 邮件头部字符串

        Returns:
            str: 解码后的字符串
        """
        result = ""
        try:
            # 处理多段编码的情况
            parts = decode_header(header_str)
            for part, encoding in parts:
                if isinstance(part, bytes):
                    # 如果提供了编码，使用该编码解码
                    if encoding:
                        result += part.decode(encoding)
                    else:
                        # 如果没有提供编码，尝试使用utf-8，失败则使用latin-1
                        try:
                            result += part.decode('utf-8')
                        except:
                            result += part.decode('latin-1')
                else:
                    # 如果是字符串，直接添加
                    result += part
            return result
        except:
            # 如果解码失败，直接返回原始字符串
            return header_str

    def _process_email_content(self, msg) -> Tuple[str, str, bool, List[str], List[str]]:
        """
        处理邮件内容和附件

        Args:
            msg: 邮件消息对象

        Returns:
            Tuple: (内容, 内容类型, 是否有附件, 附件名称列表, 附件路径列表)
        """
        content = ""
        content_type = "text"
        has_attachment = False
        attachment_names = []
        attachment_paths = []

        # 遍历邮件部分
        for part in msg.walk():
            # 跳过multipart
            if part.get_content_maintype() == 'multipart':
                continue
            if part.get_content_maintype() == 'text':
                charset = part.get_content_charset() or 'utf-8'
                try:
                    # 解码内容
                    part_content = part.get_payload(decode=True).decode(charset)
                except:
                    # 如果解码失败，尝试使用其他编码
                    try:
                        part_content = part.get_payload(decode=True).decode('utf-8')
                    except:
                        part_content = part.get_payload(decode=True).decode('latin-1', errors='ignore')

                # 更新内容类型
                if part.get_content_subtype() == 'html':
                    content_type = 'html'

                # 添加到内容
                content += part_content

        return content, content_type, has_attachment, attachment_names, attachment_paths

    def _close_connection(self, mail: imaplib.IMAP4) -> None:
        """
        关闭邮件服务器连接

        Args:
            mail: 邮件服务器连接对象
        """
        try:
            mail.close()
            mail.logout()
            self.logger.info("🔒 已关闭邮箱连接")
        except:
            self.logger.warning("⚠️ 关闭邮箱连接时出现问题，这可能是正常的")