#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉转单工单提取处理器
负责处理邮件中提取的工单号，获取工单详情并保存
"""
import json
from typing import Dict, Any

from spiders.base.utils.human import HumanBehaviorSimulator
from spiders.base.utils.logger import ProgressLogger
from spiders.framework import BaseProcessor, Status
from spiders.tesla.dao import TeslaOrderDAO
from spiders.tesla_transfer_order.api import TeslaRepairAPI
from spiders.tesla_transfer_order.dao import TslEmailDAO


class OrderExtractor(BaseProcessor):
    """
    工单提取处理器，负责处理邮件中提取的工单号，获取工单详情并保存
    """

    MODULE_NAME = 'tesla_transfer_order'
    PROCESS_NAME = '工单提取处理器'
    
    def __init__(self, config):
        """
        初始化处理器
        
        Args:
            config: 配置管理器
        """
        super().__init__(config)

        self.dao = TslEmailDAO(config)
        self.orderDao  = TeslaOrderDAO(config)
        self.tesla_api = TeslaRepairAPI(config)

    def process(self, context: Dict[str, Any]) -> Status:
        """
        执行工单提取处理逻辑
        
        Args:
            context: 处理上下文
            
        Returns:
            Status: 处理结果状态
        """
        try:
            # 初始化子流程状态
            process_status = {
                "获取邮件": {"status": "未开始", "message": ""},
                "工单提取": {"status": "未开始", "message": ""},
                "工单保存": {"status": "未开始", "message": ""}
            }
            
            self.logger.info("=" * 80)
            self.logger.info("📋 工单提取处理器开始执行")
            self.logger.info("=" * 80)
            
            # 1. 获取状态为2的邮件（已提取工单号的邮件）
            self.logger.info("🔍 正在获取已处理但未提取工单详情的邮件")
            process_status["获取邮件"]["status"] = "进行中"
            
            emails = self.dao.get_emails_by_status(1)
            # 过滤retry_count>3的邮件
            emails = [email for email in emails if email.retry_count < 3]
            
            if not emails:
                self.logger.info("ℹ️ 没有需要提取工单详情的邮件")
                process_status["获取邮件"]["status"] = "完成"
                process_status["获取邮件"]["message"] = "没有需要提取工单详情的邮件"
                
                # 打印子流程状态摘要
                self._print_process_status_summary(process_status)
                return Status(True, "没有需要提取工单详情的邮件")
                
            self.logger.info(f"📋 找到 {len(emails)} 封需要提取工单详情的邮件")
            process_status["获取邮件"]["status"] = "成功"
            process_status["获取邮件"]["message"] = f"{len(emails)} 封需要提取工单详情的邮件"
            
            # 2. 处理邮件
            self.logger.info("🔄 开始提取工单详情")
            process_status["工单提取"]["status"] = "进行中"
            
            total_orders = 0
            processed_count = 0
            success_count = 0
            failed_count = 0
            extracted_orders = []
            
            for email_obj in emails:
                processed_count += 1

                # 使用ProgressLogger输出进度条
                ProgressLogger.log_progress(logger=self.logger,current=processed_count,total=len(emails),prefix="处理邮件",emoji="📩")

                try:
                    # 获取邮件中提取的工单号
                    order_numbers = json.loads(email_obj.extract_orders) if email_obj.extract_orders else []

                    if not order_numbers:
                        self.logger.warning(f"⚠️ 邮件 {email_obj.email_id} 没有提取到工单号")
                        self.dao.update_email_status(email_obj.email_id,2,"没有提取到工单号",order_numbers)
                        continue

                    self.logger.info(f"🔎 处理邮件 {email_obj.subject[:30]}{'...' if len(email_obj.subject) > 30 else ''} 中的 {len(order_numbers)} 个工单号")

                    email_success_count = 0
                    email_failed_count = 0

                    # 处理每个工单号
                    for order_no in order_numbers:
                        try:
                            # 调用Tesla PP API获取工单详情
                            self.logger.info(f"🔄 通过Tesla API获取工单 {order_no} 详情")

                            # 添加随机延迟，模拟人类行为
                            HumanBehaviorSimulator.random_delay()

                            # 查询pp系统工单
                            item_info = self.tesla_api.search_repair_order(auth_info=context['auth_info'], order_no=order_no)

                            if item_info:
                                # 查询pp系统工单详情
                                self.tesla_api.get_order_detail(auth_info=context['auth_info'], item=item_info)

                                # 构建工单对象
                                order = self.orderDao.build_order(item_info)

                                # 检查工单是否已存在
                                exist_order = self.orderDao.get_order_by_company_order_no(order_no)
                                if exist_order:
                                    self.logger.info(f"ℹ️ 工单 {order_no} 已存在，跳过")
                                    email_success_count += 1
                                    continue
                                # 保存到数据库
                                saved_order = self.orderDao.add_order(order)

                                if saved_order:
                                    self.logger.info(f"✅ 成功保存工单: {order_no}")
                                    extracted_orders.append(saved_order)
                                    email_success_count += 1
                                    success_count += 1
                                else:
                                    self.logger.warning(f"⚠️ 保存工单 {order_no} 失败")
                                    email_failed_count += 1
                                    failed_count += 1
                            else:
                                self.logger.warning(f"⚠️ 无法获取工单 {order_no} 详情")
                                email_failed_count += 1
                                failed_count += 1

                        except Exception as order_err:
                            self.logger.error(f"❌ 获取工单 {order_no} 详情时出错: {str(order_err)}")
                            email_failed_count += 1
                            failed_count += 1

                    # 统计当前邮件处理结果
                    total_orders += len(order_numbers)

                    # 更新邮件状态
                    if email_failed_count == 0:
                        # 所有工单都成功处理
                        self.dao.update_email_status(
                            email_obj.email_id,
                            2,
                            f"成功提取 {email_success_count}/{len(order_numbers)} 个工单详情",
                            None
                        )
                    else:
                        # 有部分工单处理失败，保持状态为1，等待下次处理
                        if email_failed_count == len(order_numbers):
                            process_info =  f"提取失败: {email_failed_count}/{len(order_numbers)} 个工单详情"
                        else:
                            process_info = f"部分提取成功: {email_failed_count}/{len(order_numbers)}，失败: {email_failed_count}"
                        self.dao.update_email_status(
                            email_obj.email_id,
                            1,
                            process_info,
                            None,
                            # 如果重试次数为空设置为0，重试次数加1
                            retry_count=email_obj.retry_count+1 if email_obj.retry_count else 1
                        )

                except Exception as e:
                    self.logger.error(f"❌ 处理邮件 {email_obj.email_id} 工单详情时发生错误: {str(e)}")
                    failed_count += len(json.loads(email_obj.extract_orders) if email_obj.extract_orders else [])
                    self.dao.update_email_status(
                        email_obj.email_id,
                        1,
                        f"提取失败: {str(e)[:500]}",
                        None,
                        # 如果重试次数为空设置为0，重试次数加1
                        retry_count=email_obj.retry_count + 1 if email_obj.retry_count else 0
                    )
                        
            # 处理完成
            process_status["工单提取"]["status"] = "成功"
            process_status["工单提取"]["message"] = f"成功获取 {success_count}/{total_orders} 个工单详情"
            
            process_status["工单保存"]["status"] = "成功"
            process_status["工单保存"]["message"] = f"成功保存 {success_count} 个工单"
            
            # 打印子流程状态摘要
            self._print_process_status_summary(process_status)
            
            # 将提取的工单添加到上下文
            context['extracted_orders'] = extracted_orders
            
            self.logger.info("=" * 80)
            self.logger.info("✅ 工单提取处理器执行完成")
            self.logger.info("=" * 80)
            
            return Status(
                True, 
                f"工单提取完成，成功提取 {success_count}/{total_orders} 个工单详情", 
                {"total_orders": total_orders, "success_count": success_count, "failed_count": failed_count}
            )
            
        except Exception as e:
            self.logger.error(f"❌ 工单提取过程出现异常: {str(e)}", exc_info=True)
            return Status(False, f"工单提取异常: {str(e)}")