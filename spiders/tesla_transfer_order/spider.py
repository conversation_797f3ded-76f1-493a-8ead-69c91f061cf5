#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
特斯拉维修工单邮件爬虫模块
负责扫描邮箱、处理维修工单和发送通知
"""
from spiders.framework import GenericSpider, Pipeline
from spiders.tesla_transfer_order.processors import AuthProcessor, EmailScanner, EmailProcessor, OrderExtractor


class TeslaRepairSpider(GenericSpider):
    """特斯拉维修工单邮件爬虫"""
    
    MODULE_NAME = 'tesla_transfer_order'
    
    def __init__(self, config):
        """
        初始化特斯拉爬虫

        Args:
            config: 配置管理器实例
        """
        # 调用父类初始化
        super().__init__(config)

    def _create_pipeline(self) -> Pipeline:
        return Pipeline(self.config, [
            AuthProcessor(self.config),      # 认证处理器
            EmailScanner(self.config),       # 邮件扫描器
            EmailProcessor(self.config),     # 邮件处理器
            OrderExtractor(self.config),     # 工单提取器
        ])

