#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试163邮箱的IMAP文件夹列表
"""
import imaplib
import ssl
import socket
import time

def test_163_folders():
    """测试163邮箱的文件夹列表"""

    # 邮箱配置
    imap_server = "imap.163.com"
    imap_port = 993
    username = "<EMAIL>"
    password = "QWwLfrAH2Ni6uhbw"

    try:
        print(f"正在连接到 {imap_server}:{imap_port}")

        # 设置socket超时
        socket.setdefaulttimeout(10)

        # 测试DNS解析
        start_time = time.time()
        print("正在解析DNS...")
        ip = socket.gethostbyname(imap_server)
        dns_time = time.time() - start_time
        print(f"DNS解析完成: {imap_server} -> {ip} (耗时: {dns_time:.2f}秒)")

        # 测试TCP连接
        start_time = time.time()
        print("正在建立TCP连接...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((ip, imap_port))
        tcp_time = time.time() - start_time
        print(f"TCP连接建立成功 (耗时: {tcp_time:.2f}秒)")
        sock.close()

        # 建立IMAP SSL连接
        start_time = time.time()
        print("正在建立IMAP SSL连接...")

        # 创建SSL上下文
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE

        # 连接到IMAP服务器
        mail = imaplib.IMAP4_SSL(imap_server, imap_port, ssl_context=context)
        ssl_time = time.time() - start_time
        print(f"IMAP SSL连接建立成功 (耗时: {ssl_time:.2f}秒)")

        print(f"正在登录用户: {username}")
        # 登录
        mail.login(username, password)

        # 发送IMAP ID信息（163邮箱要求）
        try:
            print("发送IMAP ID信息...")

            # 使用原始socket发送ID命令
            id_cmd = 'A001 ID ("name" "Tesla Transfer Order Spider" "version" "1.0.0" "vendor" "Tesla Spider" "support-email" "<EMAIL>")\r\n'
            mail.sock.send(id_cmd.encode())

            # 读取响应
            response = mail.sock.recv(1024).decode()
            print(f"ID命令响应: {response.strip()}")
            print("✅ 已发送IMAP ID信息")
        except Exception as id_err:
            print(f"⚠️ 发送IMAP ID信息失败: {id_err}")
            # 继续执行，不让ID失败阻止后续操作

        print("获取文件夹列表...")
        # 获取所有文件夹
        status, folders = mail.list()

        if status == 'OK':
            print("文件夹列表:")
            for folder in folders:
                print(f"  {folder}")

            print("\n解码后的文件夹列表:")
            for folder in folders:
                # 解码文件夹名称
                folder_str = folder.decode('utf-8') if isinstance(folder, bytes) else str(folder)
                print(f"  {folder_str}")
        else:
            print(f"获取文件夹列表失败: {status}")

        # 测试选择INBOX
        print("\n测试选择INBOX文件夹...")
        status, data = mail.select('INBOX')
        if status == 'OK':
            print(f"成功选择INBOX文件夹: {data}")
        else:
            print(f"选择INBOX文件夹失败: {status}, {data}")

        # 测试选择收件箱
        print("\n测试选择'收件箱'文件夹...")
        try:
            status, data = mail.select('收件箱')
            if status == 'OK':
                print(f"成功选择'收件箱'文件夹: {data}")
            else:
                print(f"选择'收件箱'文件夹失败: {status}, {data}")
        except Exception as e:
            print(f"选择'收件箱'文件夹异常: {e}")

        # 关闭连接
        mail.close()
        mail.logout()
        print("连接已关闭")

    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_163_folders()
