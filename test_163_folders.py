#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试163邮箱的IMAP文件夹列表
"""
import imaplib
import ssl

def test_163_folders():
    """测试163邮箱的文件夹列表"""
    
    # 邮箱配置
    imap_server = "imap.163.com"
    imap_port = 993
    username = "<EMAIL>"
    password = "QWwLfrAH2Ni6uhbw"
    
    try:
        print(f"正在连接到 {imap_server}:{imap_port}")
        
        # 连接到IMAP服务器
        mail = imaplib.IMAP4(imap_server, imap_port)
        
        print(f"正在登录用户: {username}")
        # 登录
        mail.login(username, password)
        
        print("获取文件夹列表...")
        # 获取所有文件夹
        status, folders = mail.list()
        
        if status == 'OK':
            print("文件夹列表:")
            for folder in folders:
                print(f"  {folder}")
                
            print("\n解码后的文件夹列表:")
            for folder in folders:
                # 解码文件夹名称
                folder_str = folder.decode('utf-8') if isinstance(folder, bytes) else str(folder)
                print(f"  {folder_str}")
        else:
            print(f"获取文件夹列表失败: {status}")
            
        # 测试选择INBOX
        print("\n测试选择INBOX文件夹...")
        status, data = mail.select('INBOX')
        if status == 'OK':
            print(f"成功选择INBOX文件夹: {data}")
        else:
            print(f"选择INBOX文件夹失败: {status}, {data}")
            
        # 测试选择收件箱
        print("\n测试选择'收件箱'文件夹...")
        try:
            status, data = mail.select('收件箱')
            if status == 'OK':
                print(f"成功选择'收件箱'文件夹: {data}")
            else:
                print(f"选择'收件箱'文件夹失败: {status}, {data}")
        except Exception as e:
            print(f"选择'收件箱'文件夹异常: {e}")
            
        # 关闭连接
        mail.close()
        mail.logout()
        print("连接已关闭")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_163_folders()
