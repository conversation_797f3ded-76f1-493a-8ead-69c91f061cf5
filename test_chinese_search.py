#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文主题搜索功能
"""
import imaplib
import ssl
import socket
import time
import email
from email.header import decode_header

def test_chinese_search():
    """测试中文主题搜索"""
    
    # 邮箱配置
    imap_server = "imap.163.com"
    imap_port = 993
    username = "<EMAIL>"
    password = "QWwLfrAH2Ni6uhbw"
    
    # 搜索条件
    subject_filter = "订单转入"
    
    try:
        print(f"正在连接到 {imap_server}:{imap_port}")
        
        # 设置socket超时
        socket.setdefaulttimeout(10)
        
        # 创建SSL上下文
        context = ssl.create_default_context()
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        # 连接到IMAP服务器
        mail = imaplib.IMAP4_SSL(imap_server, imap_port, ssl_context=context)
        
        print(f"正在登录用户: {username}")
        # 登录
        mail.login(username, password)
        
        # 发送IMAP ID信息
        try:
            id_cmd = 'A001 ID ("name" "Tesla Transfer Order Spider" "version" "1.0.0" "vendor" "Tesla Spider" "support-email" "<EMAIL>")\r\n'
            mail.sock.send(id_cmd.encode())
            response = mail.sock.recv(1024).decode()
            print(f"ID命令响应: {response.strip()}")
        except Exception as id_err:
            print(f"⚠️ 发送IMAP ID信息失败: {id_err}")
        
        # 选择INBOX
        status, data = mail.select('INBOX')
        if status != 'OK':
            raise Exception(f"选择INBOX失败: {status}, {data}")
        print(f"成功选择INBOX文件夹: {data}")
        
        # 搜索最近的邮件（不包含中文主题过滤）
        print("搜索最近的邮件...")
        search_query = 'SINCE 01-Jun-2025'
        status, email_ids = mail.search(None, search_query)
        
        if status != 'OK':
            raise Exception(f"搜索邮件失败: {status}")
        
        email_list = email_ids[0].split() if email_ids[0] else []
        print(f"找到 {len(email_list)} 封邮件")
        
        # 检查前几封邮件的主题
        matched_emails = []
        for i, email_id in enumerate(email_list[:10]):  # 只检查前10封
            try:
                # 获取邮件头部
                status, data = mail.fetch(email_id, '(RFC822.HEADER)')
                if status != 'OK':
                    continue
                    
                # 解析邮件头部
                header_data = data[0][1]
                msg = email.message_from_bytes(header_data)
                
                # 获取主题
                subject_raw = msg.get('Subject', '')
                subject = decode_header_str(subject_raw)
                
                # 获取发件人
                sender_raw = msg.get('From', '')
                sender = decode_header_str(sender_raw)
                
                print(f"邮件 {i+1}: {subject[:50]}{'...' if len(subject) > 50 else ''}")
                print(f"  发件人: {sender}")
                
                # 检查是否匹配中文主题
                if subject_filter in subject:
                    matched_emails.append((email_id, subject, sender))
                    print(f"  ✅ 匹配主题过滤条件: {subject_filter}")
                else:
                    print(f"  ❌ 不匹配主题过滤条件")
                    
            except Exception as e:
                print(f"  处理邮件 {email_id} 时出错: {e}")
        
        print(f"\n总结:")
        print(f"搜索到的邮件总数: {len(email_list)}")
        print(f"匹配中文主题 '{subject_filter}' 的邮件数: {len(matched_emails)}")
        
        if matched_emails:
            print(f"\n匹配的邮件:")
            for email_id, subject, sender in matched_emails:
                print(f"  ID: {email_id.decode()}")
                print(f"  主题: {subject}")
                print(f"  发件人: {sender}")
                print()
        
        # 关闭连接
        mail.close()
        mail.logout()
        print("连接已关闭")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

def decode_header_str(header_str: str) -> str:
    """解码邮件头部字符串"""
    result = ""
    try:
        parts = decode_header(header_str)
        for part, encoding in parts:
            if isinstance(part, bytes):
                if encoding:
                    result += part.decode(encoding)
                else:
                    try:
                        result += part.decode('utf-8')
                    except:
                        result += part.decode('latin-1')
            else:
                result += part
        return result
    except:
        return header_str

if __name__ == "__main__":
    test_chinese_search()
