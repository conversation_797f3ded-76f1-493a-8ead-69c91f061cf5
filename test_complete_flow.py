#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的邮件处理流程测试
"""
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_email_scanner():
    """测试邮件扫描器"""
    try:
        from spiders.base.config import ConfigManager
        from spiders.tesla_transfer_order.processors.email_scanner import EmailScanner
        
        print("🧪 测试邮件扫描器")
        print("=" * 60)
        
        # 创建配置管理器
        config = ConfigManager('conf/tesla_transfer_order_config.yaml', 'dev')
        
        # 创建邮件扫描器
        scanner = EmailScanner(config)
        
        print(f"✅ 邮件扫描器创建成功")
        print(f"📧 IMAP服务器: {scanner.imap_server}:{scanner.imap_port}")
        print(f"👤 用户名: {scanner.username}")
        print(f"📁 扫描文件夹: {scanner.scan_folder}")
        print(f"🔍 发件人过滤: {scanner.sender_filter}")
        print(f"📝 主题过滤: {scanner.subject_filter}")
        print(f"📅 扫描天数: {scanner.days_back}")
        
        # 测试主题匹配功能
        test_subjects = [
            "Daomeijia-订单转入通知-JB-CN1002209114-00-江苏省",
            "Daomeijia-订单转出通知-JB-CN1002211407-00-上海市",
            "普通邮件主题"
        ]
        
        print(f"\n🔍 测试主题过滤功能:")
        for subject in test_subjects:
            is_match = scanner._match_subject_filter(subject)
            status = "✅ 匹配" if is_match else "❌ 不匹配"
            print(f"  {status}: {subject[:50]}{'...' if len(subject) > 50 else ''}")
        
        return True
        
    except Exception as e:
        print(f"❌ 邮件扫描器测试失败: {e}")
        return False

def test_email_processor():
    """测试邮件处理器"""
    try:
        from spiders.base.config import ConfigManager
        from spiders.tesla_transfer_order.processors.email_processor import EmailProcessor
        
        print(f"\n🧪 测试邮件处理器")
        print("=" * 60)
        
        # 创建配置管理器
        config = ConfigManager('conf/tesla_transfer_order_config.yaml', 'dev')
        
        # 创建邮件处理器
        processor = EmailProcessor(config)
        
        print(f"✅ 邮件处理器创建成功")
        
        # 测试工单号提取功能
        test_titles = [
            "Daomeijia-订单转入通知-JB-CN1002209114-00-江苏省",
            "订单转入通知-JB-CN1002212414-00",
            "普通邮件主题"
        ]
        
        print(f"\n🔍 测试工单号提取功能:")
        for title in test_titles:
            order_numbers = processor._extract_order_numbers_from_title(title)
            if order_numbers:
                print(f"  ✅ 提取成功: {title} -> {order_numbers}")
            else:
                print(f"  ❌ 未提取到: {title}")
        
        return True
        
    except Exception as e:
        print(f"❌ 邮件处理器测试失败: {e}")
        return False

def test_dao():
    """测试数据访问对象"""
    try:
        from spiders.base.config import ConfigManager
        from spiders.tesla_transfer_order.dao import TslEmailDAO
        
        print(f"\n🧪 测试数据访问对象")
        print("=" * 60)
        
        # 创建配置管理器
        config = ConfigManager('conf/tesla_transfer_order_config.yaml', 'dev')
        
        # 创建DAO
        dao = TslEmailDAO(config)
        
        print(f"✅ DAO创建成功")
        print(f"📊 数据库URL: {config.get('database.url', '未配置')}")
        
        return True
        
    except Exception as e:
        print(f"❌ DAO测试失败: {e}")
        return False

def test_spider():
    """测试爬虫主类"""
    try:
        from spiders.base.config import ConfigManager
        from spiders.tesla_transfer_order.spider import TeslaRepairSpider
        
        print(f"\n🧪 测试爬虫主类")
        print("=" * 60)
        
        # 创建配置管理器
        config = ConfigManager('conf/tesla_transfer_order_config.yaml', 'dev')
        
        # 创建爬虫
        spider = TeslaRepairSpider(config)
        
        print(f"✅ 爬虫创建成功")
        print(f"📋 模块名称: {spider.MODULE_NAME}")
        
        # 创建处理管道
        pipeline = spider._create_pipeline()
        print(f"✅ 处理管道创建成功，包含 {len(pipeline.processors)} 个处理器")
        
        for i, processor in enumerate(pipeline.processors, 1):
            print(f"  {i}. {processor.__class__.__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 爬虫测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始完整的邮件处理流程测试")
    print("=" * 80)
    
    tests = [
        ("邮件扫描器", test_email_scanner),
        ("邮件处理器", test_email_processor),
        ("数据访问对象", test_dao),
        ("爬虫主类", test_spider)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}测试通过")
            else:
                print(f"\n❌ {test_name}测试失败")
        except Exception as e:
            print(f"\n❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！邮件处理流程准备就绪")
    else:
        print("⚠️ 部分测试失败，请检查相关配置")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
