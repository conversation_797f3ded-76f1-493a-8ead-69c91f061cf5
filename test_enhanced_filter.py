#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的客户端主题过滤功能
"""
import re

def test_subject_filter():
    """测试主题过滤功能"""
    
    # 模拟邮件主题列表
    test_subjects = [
        "Daomeijia-订单转入通知-JB-CN1002209114-00-江苏省",
        "Daomeijia-订单转出通知-JB-CN1002211407-00-上海市", 
        "Tesla订单转入提醒",
        "订单 转入 通知",
        "订单转入",
        "转入订单通知",
        "Order Transfer In Notification",
        "普通邮件主题",
        "Daomeijia-维修订单-JB-CN1002209114-00",
        "订单转入通知（重要）",
        "【订单转入】通知邮件"
    ]
    
    # 测试过滤条件
    filter_conditions = [
        "订单转入",
        "转入",
        "订单 转入",
        "Order Transfer"
    ]
    
    print("=" * 80)
    print("🧪 测试增强的客户端主题过滤功能")
    print("=" * 80)
    
    for filter_condition in filter_conditions:
        print(f"\n📝 测试过滤条件: '{filter_condition}'")
        print("-" * 60)
        
        matched_count = 0
        for subject in test_subjects:
            is_match = match_subject_filter(subject, filter_condition)
            status = "✅ 匹配" if is_match else "❌ 不匹配"
            print(f"  {status}: {subject}")
            if is_match:
                matched_count += 1
        
        print(f"\n📊 匹配结果: {matched_count}/{len(test_subjects)} 封邮件匹配")

def match_subject_filter(subject: str, subject_filter: str) -> bool:
    """
    检查邮件主题是否匹配过滤条件
    
    Args:
        subject: 邮件主题
        subject_filter: 过滤条件
        
    Returns:
        bool: 是否匹配
    """
    if not subject_filter:
        return True
        
    # 转换为小写进行比较（不区分大小写）
    subject_lower = subject.lower()
    filter_lower = subject_filter.lower()
    
    # 支持多种匹配方式
    match_methods = [
        # 1. 直接包含匹配
        lambda s, f: f in s,
        # 2. 去除空格后匹配
        lambda s, f: f.replace(' ', '') in s.replace(' ', ''),
        # 3. 分词匹配（所有关键词都存在）
        lambda s, f: all(word in s for word in f.split()),
        # 4. 模糊匹配（去除标点符号）
        lambda s, f: remove_punctuation(f) in remove_punctuation(s)
    ]
    
    # 尝试各种匹配方式
    for i, method in enumerate(match_methods, 1):
        try:
            if method(subject_lower, filter_lower):
                print(f"    ✅ 匹配方式{i}成功")
                return True
        except Exception as e:
            print(f"    ⚠️ 匹配方式{i}失败: {e}")
            continue
    
    return False

def remove_punctuation(text: str) -> str:
    """
    移除文本中的标点符号
    
    Args:
        text: 输入文本
        
    Returns:
        str: 移除标点符号后的文本
    """
    # 移除常见的中英文标点符号
    return re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)

def test_punctuation_removal():
    """测试标点符号移除功能"""
    print("\n" + "=" * 80)
    print("🧪 测试标点符号移除功能")
    print("=" * 80)
    
    test_texts = [
        "Daomeijia-订单转入通知-JB-CN1002209114-00-江苏省",
        "订单转入通知（重要）",
        "【订单转入】通知邮件",
        "Order Transfer In Notification",
        "Tesla-订单-转入-提醒"
    ]
    
    for text in test_texts:
        cleaned = remove_punctuation(text)
        print(f"原文: {text}")
        print(f"清理后: {cleaned}")
        print()

if __name__ == "__main__":
    test_subject_filter()
    test_punctuation_removal()
    
    print("\n" + "=" * 80)
    print("✅ 测试完成")
    print("=" * 80)
